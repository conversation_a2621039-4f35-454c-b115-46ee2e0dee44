// ==UserScript==
// @name         Kimi聊天消息列表宽度自动增加
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动增加Kimi聊天页面的消息列表宽度，提供更好的阅读体验
// <AUTHOR>
// @match        https://kimi.moonshot.cn/*
// @match        https://www.kimi.com/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // 等待页面加载完成
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                    return;
                }
                
                setTimeout(check, 100);
            }
            
            check();
        });
    }

    // 添加自定义CSS样式
    function addCustomStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* 主要布局调整 - 考虑左侧菜单栏 */
            body {
                overflow-x: hidden !important;
            }

            /* 主容器布局优化 */
            .layout-container,
            .main-content,
            .content-wrapper,
            [class*="layout"],
            [class*="main"] {
                max-width: none !important;
                width: 100% !important;
                margin: 0 auto !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
            }

            /* 聊天区域容器 - 居中显示，考虑左侧菜单 */
            .chat-container,
            .chat-content,
            .message-list,
            .conversation-container,
            [class*="chat-"],
            [class*="conversation"] {
                max-width: 85% !important;
                width: 85% !important;
                margin: 0 auto !important;
                padding: 0 20px !important;
                box-sizing: border-box !important;
            }

            /* 消息容器居中 */
            [class*="message"],
            [class*="chat"]:not([class*="container"]) {
                max-width: 100% !important;
                margin: 0 auto !important;
            }

            /* 消息气泡宽度优化 */
            .message-bubble,
            .chat-bubble,
            .message-content,
            [class*="bubble"],
            [class*="message-content"] {
                max-width: 95% !important;
                word-wrap: break-word !important;
                word-break: break-word !important;
            }

            /* 滚动条样式优化 - 放到最右侧 */
            * {
                scrollbar-width: thin !important;
                scrollbar-color: #888 transparent !important;
            }

            *::-webkit-scrollbar {
                width: 8px !important;
                height: 8px !important;
            }

            *::-webkit-scrollbar-track {
                background: transparent !important;
            }

            *::-webkit-scrollbar-thumb {
                background: #888 !important;
                border-radius: 4px !important;
            }

            *::-webkit-scrollbar-thumb:hover {
                background: #555 !important;
            }

            /* 确保滚动条在最右侧 */
            .chat-container,
            .message-list,
            [class*="scroll"] {
                overflow-y: auto !important;
                overflow-x: hidden !important;
            }

            /* 左侧菜单栏保持原样 */
            [class*="sidebar"],
            [class*="menu"],
            [class*="nav"],
            .sidebar,
            .menu,
            .navigation {
                position: relative !important;
                z-index: 1000 !important;
            }

            /* 主内容区域适配 */
            [class*="main-panel"],
            [class*="content-panel"],
            [class*="chat-panel"] {
                margin-left: 0 !important;
                padding-left: 0 !important;
                width: 100% !important;
                display: flex !important;
                justify-content: center !important;
            }

            /* 响应式调整 */
            @media (max-width: 1200px) {
                .chat-container,
                .chat-content,
                .message-list,
                .conversation-container {
                    max-width: 90% !important;
                    width: 90% !important;
                }
            }

            @media (max-width: 768px) {
                .chat-container,
                .chat-content,
                .message-list,
                .conversation-container {
                    max-width: 95% !important;
                    width: 95% !important;
                    padding: 0 10px !important;
                }
            }
        `;
        document.head.appendChild(style);
        console.log('Kimi宽度扩展样式已添加');
    }

    // 动态调整元素宽度和布局
    function adjustElementWidths() {
        // 聊天容器选择器
        const chatSelectors = [
            '[class*="chat-container"]',
            '[class*="chat-content"]',
            '[class*="message-list"]',
            '[class*="conversation"]',
            '[class*="chat-panel"]'
        ];

        // 主容器选择器
        const mainSelectors = [
            '[class*="main-content"]',
            '[class*="content-wrapper"]',
            '[class*="layout-container"]',
            '.main',
            '#main',
            '[role="main"]'
        ];

        // 调整聊天容器 - 居中显示
        chatSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                const computedStyle = window.getComputedStyle(element);
                const maxWidth = computedStyle.maxWidth;

                // 设置聊天容器居中
                if (maxWidth && maxWidth !== 'none' && parseInt(maxWidth) < window.innerWidth * 0.8) {
                    element.style.maxWidth = '85%';
                    element.style.width = '85%';
                    element.style.margin = '0 auto';
                    element.style.padding = '0 20px';
                    element.style.boxSizing = 'border-box';
                }
            });
        });

        // 调整主容器 - 全宽显示
        mainSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                element.style.maxWidth = 'none';
                element.style.width = '100%';
                element.style.display = 'flex';
                element.style.justifyContent = 'center';
            });
        });

        // 确保滚动条在正确位置
        const scrollableElements = document.querySelectorAll('[class*="scroll"], [class*="chat"], [class*="message"]');
        scrollableElements.forEach(element => {
            if (element.scrollHeight > element.clientHeight) {
                element.style.overflowY = 'auto';
                element.style.overflowX = 'hidden';
            }
        });
    }

    // 监听DOM变化
    function observeChanges() {
        const observer = new MutationObserver((mutations) => {
            let shouldAdjust = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldAdjust = true;
                }
            });
            
            if (shouldAdjust) {
                setTimeout(adjustElementWidths, 100);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('DOM变化监听器已启动');
    }

    // 专门处理Kimi页面布局
    function optimizeKimiLayout() {
        // 查找并调整Kimi特有的布局元素
        const kimiSelectors = [
            '[class*="Layout"]',
            '[class*="ChatContainer"]',
            '[class*="MessageList"]',
            '[class*="ConversationPanel"]',
            '[data-testid*="chat"]',
            '[data-testid*="message"]'
        ];

        kimiSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                // 检查是否是聊天相关容器
                if (element.className.toLowerCase().includes('chat') ||
                    element.className.toLowerCase().includes('message') ||
                    element.className.toLowerCase().includes('conversation')) {

                    element.style.maxWidth = '85%';
                    element.style.margin = '0 auto';
                    element.style.padding = '0 20px';
                }
            });
        });

        // 特殊处理可能的React组件容器
        const reactContainers = document.querySelectorAll('[data-reactroot], [id*="root"], [class*="App"]');
        reactContainers.forEach(container => {
            container.style.display = 'flex';
            container.style.justifyContent = 'center';
            container.style.width = '100%';
        });
    }

    // 主函数
    async function init() {
        try {
            // 立即添加样式
            addCustomStyles();

            // 等待页面基本加载
            await new Promise(resolve => {
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', resolve);
                } else {
                    resolve();
                }
            });

            // 等待React应用加载
            setTimeout(() => {
                adjustElementWidths();
                optimizeKimiLayout();
            }, 1000);

            // 启动监听器
            observeChanges();

            // 定期检查和调整
            setInterval(() => {
                adjustElementWidths();
                optimizeKimiLayout();
            }, 3000);

            console.log('Kimi聊天宽度扩展脚本已启动');

        } catch (error) {
            console.error('Kimi宽度扩展脚本初始化失败:', error);
        }
    }

    // 启动脚本
    init();

})();
