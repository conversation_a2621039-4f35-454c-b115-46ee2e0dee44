// ==UserScript==
// @name         Kimi聊天消息列表宽度自动增加
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动增加Kimi聊天页面的消息列表宽度，提供更好的阅读体验
// <AUTHOR>
// @match        https://kimi.moonshot.cn/*
// @match        https://www.kimi.com/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // 等待页面加载完成
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                    return;
                }
                
                setTimeout(check, 100);
            }
            
            check();
        });
    }

    // 添加自定义CSS样式
    function addCustomStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* 增加聊天容器的最大宽度 */
            .chat-container,
            .chat-content,
            .message-list,
            .conversation-container {
                max-width: 95% !important;
                width: 95% !important;
            }
            
            /* 针对可能的聊天消息容器 */
            [class*="chat"],
            [class*="message"],
            [class*="conversation"] {
                max-width: 95% !important;
            }
            
            /* 增加消息气泡的宽度 */
            .message-bubble,
            .chat-bubble,
            .message-content {
                max-width: 90% !important;
            }
            
            /* 针对可能的布局容器 */
            .layout-container,
            .main-content,
            .content-wrapper {
                max-width: 98% !important;
                width: 98% !important;
            }
            
            /* 移除可能的固定宽度限制 */
            * {
                max-width: none !important;
            }
            
            /* 特殊处理可能的flex布局 */
            .flex-container,
            [style*="max-width"] {
                max-width: 95% !important;
            }
        `;
        document.head.appendChild(style);
        console.log('Kimi宽度扩展样式已添加');
    }

    // 动态调整元素宽度
    function adjustElementWidths() {
        // 常见的聊天容器选择器
        const selectors = [
            '[class*="chat"]',
            '[class*="message"]',
            '[class*="conversation"]',
            '[class*="content"]',
            '[class*="container"]',
            '.main',
            '#main',
            '[role="main"]'
        ];

        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                const computedStyle = window.getComputedStyle(element);
                const maxWidth = computedStyle.maxWidth;
                
                // 如果元素有较小的最大宽度限制，则扩大它
                if (maxWidth && maxWidth !== 'none' && parseInt(maxWidth) < window.innerWidth * 0.9) {
                    element.style.maxWidth = '95%';
                    element.style.width = '95%';
                }
            });
        });
    }

    // 监听DOM变化
    function observeChanges() {
        const observer = new MutationObserver((mutations) => {
            let shouldAdjust = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldAdjust = true;
                }
            });
            
            if (shouldAdjust) {
                setTimeout(adjustElementWidths, 100);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('DOM变化监听器已启动');
    }

    // 主函数
    async function init() {
        try {
            // 立即添加样式
            addCustomStyles();
            
            // 等待页面基本加载
            await new Promise(resolve => {
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', resolve);
                } else {
                    resolve();
                }
            });
            
            // 初始调整
            adjustElementWidths();
            
            // 启动监听器
            observeChanges();
            
            // 定期检查和调整
            setInterval(adjustElementWidths, 2000);
            
            console.log('Kimi聊天宽度扩展脚本已启动');
            
        } catch (error) {
            console.error('Kimi宽度扩展脚本初始化失败:', error);
        }
    }

    // 启动脚本
    init();

})();
