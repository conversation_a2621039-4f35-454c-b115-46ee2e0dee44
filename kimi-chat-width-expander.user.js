// ==UserScript==
// @name         <PERSON>i聊天消息列表宽度增加
// @namespace    http://tampermonkey.net/
// @version      3.0
// @description  仅增加Kimi聊天消息列表的宽度，不影响其他页面元素
// <AUTHOR>
// @match        https://kimi.moonshot.cn/*
// @match        https://www.kimi.com/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // 添加自定义CSS样式 - 仅针对消息列表
    function addCustomStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* 仅增加聊天消息列表宽度 */
            [class*="message-list"],
            [class*="MessageList"],
            [class*="chat-messages"],
            [class*="conversation-messages"],
            [class*="messages-container"],
            [data-testid*="message-list"],
            [data-testid*="messages"] {
                max-width: 90% !important;
                width: 90% !important;
            }

            /* 增加单个消息容器宽度 */
            [class*="message-item"],
            [class*="message-wrapper"],
            [class*="chat-message"],
            [class*="conversation-item"] {
                max-width: 95% !important;
            }

            /* 增加消息气泡宽度 */
            [class*="message-bubble"],
            [class*="message-content"],
            [class*="chat-bubble"],
            [class*="bubble"] {
                max-width: 100% !important;
                word-wrap: break-word !important;
                word-break: break-word !important;
            }

            /* 确保文本内容能够充分利用宽度 */
            [class*="message"] p,
            [class*="message"] div,
            [class*="bubble"] p,
            [class*="bubble"] div {
                max-width: 100% !important;
            }
        `;
        document.head.appendChild(style);
        console.log('Kimi消息列表宽度扩展样式已添加');
    }

    // 动态调整消息列表宽度
    function adjustMessageListWidth() {
        // 仅针对消息列表的选择器
        const messageListSelectors = [
            '[class*="message-list"]',
            '[class*="MessageList"]',
            '[class*="chat-messages"]',
            '[class*="conversation-messages"]',
            '[class*="messages-container"]',
            '[data-testid*="message-list"]',
            '[data-testid*="messages"]'
        ];

        // 消息项选择器
        const messageItemSelectors = [
            '[class*="message-item"]',
            '[class*="message-wrapper"]',
            '[class*="chat-message"]',
            '[class*="conversation-item"]'
        ];

        // 调整消息列表宽度
        messageListSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                const computedStyle = window.getComputedStyle(element);
                const maxWidth = computedStyle.maxWidth;

                // 只有当前宽度小于90%时才调整
                if (maxWidth && maxWidth !== 'none' && parseInt(maxWidth) < window.innerWidth * 0.9) {
                    element.style.maxWidth = '90%';
                    element.style.width = '90%';
                }
            });
        });

        // 调整消息项宽度
        messageItemSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                const computedStyle = window.getComputedStyle(element);
                const maxWidth = computedStyle.maxWidth;

                if (maxWidth && maxWidth !== 'none' && parseInt(maxWidth) < element.parentElement.clientWidth * 0.95) {
                    element.style.maxWidth = '95%';
                }
            });
        });
    }

    // 监听DOM变化
    function observeChanges() {
        const observer = new MutationObserver((mutations) => {
            let shouldAdjust = false;

            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // 检查是否有新的消息相关元素
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) { // Element node
                            const className = node.className || '';
                            if (typeof className === 'string' &&
                                (className.includes('message') || className.includes('chat'))) {
                                shouldAdjust = true;
                            }
                        }
                    });
                }
            });

            if (shouldAdjust) {
                setTimeout(adjustMessageListWidth, 100);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('消息列表DOM变化监听器已启动');
    }

    // 主函数
    async function init() {
        try {
            // 立即添加样式
            addCustomStyles();

            // 等待页面基本加载
            await new Promise(resolve => {
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', resolve);
                } else {
                    resolve();
                }
            });

            // 等待React应用加载后调整
            setTimeout(() => {
                adjustMessageListWidth();
            }, 1000);

            // 启动监听器
            observeChanges();

            // 定期检查和调整消息列表
            setInterval(adjustMessageListWidth, 3000);

            console.log('Kimi消息列表宽度扩展脚本已启动');

        } catch (error) {
            console.error('Kimi消息列表宽度扩展脚本初始化失败:', error);
        }
    }

    // 启动脚本
    init();

})();
